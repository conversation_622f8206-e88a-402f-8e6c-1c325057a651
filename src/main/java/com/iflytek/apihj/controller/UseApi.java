package com.iflytek.apihj.controller;

import com.iflytek.apihj.sdk.ShieldSyncApp_获取token接口__B04E4C5BCD114952A3D015855634F404;
import com.iflytek.fsp.shield.java.sdk.model.ApiResponse;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@RequestMapping("/api")
public class UseApi {

    @RequestMapping(value ="/3AED888C286B41F3A443CDAA5BE0E714",method = RequestMethod.POST)
    public String AED888C286B41F3A443CDAA5BE0E714(@RequestParam(name = "gateway_appid", required = false) String gateway_appid,
                                                  @RequestParam(name = "gateway_rtime", required = false) String gateway_rtime,
                                                  @RequestParam(name = "gateway_sig", required = false) String gateway_sig){
        ShieldSyncApp_获取token接口__B04E4C5BCD114952A3D015855634F404 app = new ShieldSyncApp_获取token接口__B04E4C5BCD114952A3D015855634F404();
        ApiResponse apiResponse = app.AED888C286B41F3A443CDAA5BE0E714(gateway_appid,gateway_rtime,gateway_sig);
        return printResponse(apiResponse);
    }
    private static String printResponse(ApiResponse response) {
        String result = null;
        try {
            result = new String(response.getBody(), "UTF-8");
            System.out.println("response code = " + response.getStatusCode());
            System.out.println("response content = " + new String(response.getBody(), "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
