package com.iflytek.apihj.sdk;

import com.iflytek.fsp.shield.java.sdk.constant.HttpConstant;
import com.iflytek.fsp.shield.java.sdk.constant.SdkConstant;
import com.iflytek.fsp.shield.java.sdk.enums.Method;
import com.iflytek.fsp.shield.java.sdk.enums.ParamPosition;
import com.iflytek.fsp.shield.java.sdk.http.ApiClient;
import com.iflytek.fsp.shield.java.sdk.http.BaseApp;
import com.iflytek.fsp.shield.java.sdk.model.*;

import java.io.File;

public class ShieldAsyncApp_获取token接口__B04E4C5BCD114952A3D015855634F404 extends BaseApp {

    public ShieldAsyncApp_获取token接口__B04E4C5BCD114952A3D015855634F404() {
        this.apiClient = new ApiClient();
        this.apiClient.init();
        this.appId = "eb875cef28c246b894acb71a886ddb0e";
        this.appSecret = "D2D22E853B3E5DE109290039C55ADC9F";
        this.tempAppSecret = "D2D22E853B3E5DE109290039C55ADC9F";
        this.host = "**************";
        this.httpPort = 9002;
        this.httpsPort = 443;
        this.stage = "RELEASE";
        this.publicKey = "305C300D06092A864886F70D0101010500034B0030480241009020EDB0570F5AE2986F127565AD35834BA3A33916746A3DD4B37F6BC3A01E8776401DCA0EA3D901341A4453ABF30CA3F5DF7DD3F3185E860C48730FD4FF66D10203010001";
        this.equipmentNo = "XXX";
        this.signStrategyUrl = "/getSignStrategy";
        this.tokenUrl = "/getTokenUrl";
        this.publicKey = "305C300D06092A864886F70D0101010500034B0030480241009020EDB0570F5AE2986F127565AD35834BA3A33916746A3DD4B37F6BC3A01E8776401DCA0EA3D901341A4453ABF30CA3F5DF7DD3F3185E860C48730FD4FF66D10203010001";
    }

  /**
    * 服务签名策略处理
    */
    private void doSignStrategy(ApiRequest apiRequest){
        //设置AppSecret
        super.setAppSecret(super.tempAppSecret);
        //获取服务安全策略信息
        ApiSignStrategy signStrategy = super.initApiSignStrategy(apiRequest.getPath());
        //判断是否需要token校验
        if(null!=signStrategy && "token".equals(signStrategy.getSignType())){
            //从本地缓存获取token信息,如果本地缓存存在token信息，验证本地缓存token的有效次数，
            //1.如果验证通过，token次数-1，回写到本地缓存；
            //2.如果验证不通过，从新获取token信息，并写到本地缓存。

            //从token服务获取token信息
            ResultInfo resultInfo = super.getTokenInfo(signStrategy);
            if(null!=resultInfo && SdkConstant.SUCCESS.equals(resultInfo.getCode())) {
                super.setAppSecret(resultInfo.getData().getTokenValue());
                apiRequest.getHeaders().put(SdkConstant.AUTH_EQUIPMENTNO,super.equipmentNo);
            }else{
                System.err.println("获取token信息失败");
            }
        }
    }


  /**
    * Version:202006281128068815
    */
    public void AED888C286B41F3A443CDAA5BE0E714(String gateway_appid, String gateway_rtime, String gateway_sig, ApiCallback apiCallback) {
        ApiRequest apiRequest = new ApiRequest(HttpConstant.SCHEME_HTTP, Method.POST, "/api/3AED888C286B41F3A443CDAA5BE0E714", SdkConstant.AUTH_TYPE_ENCRYPT, "1");
        doSignStrategy(apiRequest);


        apiRequest.addParam("gateway_appid", gateway_appid, ParamPosition.HEADER, true);

        apiRequest.addParam("gateway_rtime", gateway_rtime, ParamPosition.HEADER, true);

        apiRequest.addParam("gateway_sig", gateway_sig, ParamPosition.HEADER, true);

        asyncInvoke(apiRequest, apiCallback);
    }

}
